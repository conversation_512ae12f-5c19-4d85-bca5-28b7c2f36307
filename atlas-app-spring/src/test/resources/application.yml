logging.level:
  ROOT: INFO
tenant:
  express-id: tenant.express-id
  default-id: tenant.default-id

features-toggle:
  exclude-test-users: false
  routeone-prefix-enable: true
dynamoDB:
  dealerTable: upgrade_prospect_etl_dealer_test
  lenderDesk-table: financier-lender-desk-mapping-table-staging
  session-heartbeat-table: digital-retail-session-heartbeat-staging
features-subscription:
  routeone-f-and-i-feature-id: 2289A234-1E4D-4E80-84C6-F7853596BEB5
  nesna-f-and-i-feature-id: 76c3c23b-2b1e-4822-bd26-9ef735820641
  carsaver-f-and-i-feature-id: 3b7c1f2d-4e8a-4c5b-9f0e-6c8d1f2e3a4b
  lms-feature-id: c45bb173-dd94-42c1-b778-6736ac4e0e2c
  boost-features-feature-id: 12345678-90ab-cdef-1234-567890abcdef
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${magellen-service-api-uri}/uaa/.well-known/jwks.json
      client:
        provider:
          carsaver:
            token-uri: ${magellen-service-api-uri}/uaa/oauth/token
            jwk-set-uri: ${magellen-service-api-uri}/uaa/.well-known/jwks.json
  main:
    allow-bean-definition-overriding: true
  application:
    name: atlas-app
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 25MB
activity-service:
  api-uri: ${magellen-service-api-uri}/activity
token-service:
  username: <EMAIL>
  password: AahdCzESGsDMDA52

magellen-service-api-uri: https://api-beta.carsaver.com
