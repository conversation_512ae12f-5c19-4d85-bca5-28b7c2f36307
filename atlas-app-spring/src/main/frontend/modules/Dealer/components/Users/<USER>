<template>
    <v-card>
        <v-card-title v-if="!isLoading"> {{ dealerUser.fullName }} | {{ dealerUser.email }} </v-card-title>
        <v-card-text>
            <div v-if="isLoading">
                <v-skeleton-loader type="list-item-three-line"></v-skeleton-loader>
                <v-skeleton-loader type="list-item-three-line"></v-skeleton-loader>
                <v-skeleton-loader type="list-item-three-line"></v-skeleton-loader>
            </div>
            <v-form v-else novalidate @submit.prevent="associateUser()">
                <v-container>
                    <v-row>
                        <v-col>
                            <v-select v-model="form.contactType" label="Contact Type" :items="availableContactTypes" />
                        </v-col>
                    </v-row>

                    <v-row>
                        <v-col>
                            <v-switch v-model="form.programManager" label="Program Manager" />
                        </v-col>
                        <v-col>
                            <div v-if="form.programManager">
                                <v-select
                                    v-model="form.pmStockType"
                                    name="programManagerStockType"
                                    label="Stock Type"
                                    :items="stockTypes"
                                />
                                <small>(This will limit the leads to this stock type for this user)</small>
                            </div>
                        </v-col>
                    </v-row>

                    <v-row>
                        <v-col>
                            <v-switch v-model="form.followupManager" label="Followup Manager" />
                        </v-col>
                        <v-col>
                            <div v-if="form.followupManager">
                                <v-select
                                    v-model="form.fmStockType"
                                    name="followupManagerStockType"
                                    label="Stock Type"
                                    :items="stockTypes"
                                />
                                <small>(This will limit the leads to this stock type for this user)</small>
                            </div>
                        </v-col>
                    </v-row>

                    <v-row>
                        <v-col>
                            <v-switch v-model="form.salesManager" label="Sales Manager" />
                        </v-col>
                        <v-col>
                            <div v-if="form.salesManager">
                                <v-select
                                    v-model="form.smStockType"
                                    name="salesManagerStockType"
                                    label="Stock Type"
                                    :items="stockTypes"
                                />
                                <small>(This will limit the leads to this stock type for this user)</small>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row v-if="isDealerSalesPersonEnabled">
                        <v-col>
                            <v-switch
                                v-model="form.salesPerson"
                                label="Include in the Salesperson selector for in-store customer check-in."
                            />
                        </v-col>
                    </v-row>
                    <v-row v-if="isOnlineNowSMSEnabled">
                        <v-col>
                            <v-switch v-model="form.onlineSMSEnabled" label="SMS Alert for Online Now Prospects" />
                        </v-col>
                    </v-row>
                    <v-row v-if="isServiceDriveLeadsEnabled">
                        <v-col>
                            <v-switch v-model="form.serviceDriveLeadsEnabled" label="Receive Service Drive Leads" />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <h3>
                                Permissions
                                <v-tooltip top max-width="250px">
                                    <template #activator="{ on, attrs }">
                                        <v-icon v-attrs="attrs" small v-on="on">mdi-information-outline</v-icon>
                                    </template>
                                    <span>
                                        NOTE: Some permissions imply other permissions. Example: "Edit" pricing implies
                                        "Read" pricing. Implied permissions will be automatically checked.
                                    </span>
                                </v-tooltip>
                            </h3>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <v-treeview
                                v-if="!loading"
                                v-model="permissions"
                                item-disabled="locked"
                                selected-color="primary"
                                open-all
                                selectable
                                :item-text="'description'"
                                :item-key="'id'"
                                :items="filteredPermissions"
                            ></v-treeview>
                        </v-col>
                    </v-row>
                </v-container>
                <v-btn :loading="isAddingUser" @click="associateUser()"> Save </v-btn>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script>
import api from "Util/api";
import _ from "lodash";
import { get } from "vuex-pathify";

export default {
    name: "DealerPermissionsForm",
    props: {
        dealerUser: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            stockTypes: [
                { text: "NEW & USED", value: "Unknown" },
                { text: "NEW", value: "New" },
                { text: "USED", value: "Used" },
            ],
            availablePermissions: [],
            availableJobTitles: [],
            availableContactTypes: [
                { text: "None", value: null },
                { text: "Primary", value: "Primary" },
                { text: "Secondary", value: "Secondary" },
                { text: "User Champion", value: "User_Champion" },
            ],
            disabled: false,
            isAddingUser: false,
            form: {
                programManager: false,
                followupManager: false,
                salesManager: false,
                salesPerson: false,
                pmStockType: null,
                fmStockType: null,
                smStockType: null,
                permissions: [],
                contactType: null,
                onlineSMSEnabled: false,
                serviceDriveLeadsEnabled: false,
            },
            loading: true,
            permissions: [],
            smsPermissionId: null,
            serviceDriveLeadsPermissionId: null,
            isLoading: false,
        };
    },
    computed: {
        isDealerSalesPersonEnabled: get("loggedInUser/featureFlags@ENABLE_DEALER_SALES_PERSON"),
        isOnlineNowSMSEnabled: get("loggedInUser/featureFlags@ENABLE_SMS_ONLINE_NOW"),
        isServiceDriveLeadsEnabled: get("loggedInUser/featureFlags@ATLAS_SERVICE_DRIVE_TOGGLE"),
        filteredPermissions() {
            return this.filterSMSPermissions(this.availablePermissions);
        },
    },
    watch: {
        permissions: {
            deep: true,
            handler(newPerms, oldPerms) {
                this.handleAddImpliedPermissionsIfApplicable(newPerms, oldPerms);
            },
        },
        "form.onlineSMSEnabled": {
            handler(newValue) {
                this.handleSMSToggleChange(newValue);
            },
        },
    },
    mounted() {
        this.fetchPermissions().then(() => {
            this.form = {
                ...this.form,
                ...this.dealerUser,
            };

            if (!_.isEmpty(this.dealerUser.permissions)) {
                this.permissions = _.map(this.dealerUser.permissions, "id");

                // Check if SMS permission is already selected and set the toggle accordingly
                if (this.smsPermissionId && this.permissions.includes(this.smsPermissionId)) {
                    this.form.onlineSMSEnabled = true;
                }
            }
            this.loading = false;
        });
    },
    methods: {
        handleAddImpliedPermissionsIfApplicable(newPerms, oldPerms) {
            if (!_.isNil(newPerms) && !_.isEqual(newPerms, oldPerms)) {
                const flatPerms = this.flattenPermissionGroups(this.availablePermissions);
                _.forEach(newPerms, (newPerm) => {
                    _.forEach(flatPerms, (flatPerm) => {
                        const addImpliedPermissionsIfNotAlreadySelected =
                            newPerm === flatPerm.id && !_.isEmpty(flatPerm.impliedPermissions);
                        if (addImpliedPermissionsIfNotAlreadySelected) {
                            this.addImpliedPermissionsIfNotAlreadySelected(newPerms, flatPerm.impliedPermissions);
                        }
                    });
                });
            }
        },
        flattenPermissionGroups(permissionGroups) {
            return _.flatMap(permissionGroups, (grp) => {
                return !_.isEmpty(grp.children) ? this.flattenPermissionGroups(grp.children) : [grp];
            });
        },
        addImpliedPermissionsIfNotAlreadySelected(permissions, impliedPermissions) {
            let impliedPermissionAlreadySelected = false;
            _.forEach(impliedPermissions, (impliedPermission) => {
                _.forEach(permissions, (newPerm) => {
                    if (newPerm === impliedPermission) {
                        impliedPermissionAlreadySelected = true;
                    }
                });
                if (!impliedPermissionAlreadySelected) {
                    permissions.push(impliedPermission);
                }
            });
        },
        filterSMSPermissions(permissions) {
            if (!permissions || !Array.isArray(permissions)) {
                return [];
            }

            return permissions
                .map((permission) => {
                    // Create a deep copy of the permission
                    const filteredPermission = JSON.parse(JSON.stringify(permission));

                    // Check if this is the SMS permission we want to filter out
                    const isSMSPermission = permission.id && permission.id === 41;

                    // Store the SMS permission ID for later use
                    if (isSMSPermission) {
                        this.smsPermissionId = permission.id;
                        return null;
                    }

                    // Recursively filter children
                    if (filteredPermission.children && filteredPermission.children.length > 0) {
                        filteredPermission.children = this.filterSMSPermissions(filteredPermission.children);
                    }

                    return filteredPermission;
                })
                .filter((permission) => permission !== null);
        },
        handleSMSToggleChange(isEnabled) {
            if (this.smsPermissionId === null) {
                return;
            }

            if (isEnabled) {
                // Add SMS permission to the permissions array if not already present
                if (!this.permissions.includes(this.smsPermissionId)) {
                    this.permissions.push(this.smsPermissionId);
                }
            } else {
                // Remove SMS permission from the permissions array
                const index = this.permissions.indexOf(this.smsPermissionId);
                if (index > -1) {
                    this.permissions.splice(index, 1);
                }
            }
        },
        fetchPermissions() {
            this.isLoading = true;
            return api
                .get(`/dealer/${this.dealerId}/users/form-data`)
                .then((response) => {
                    const _availablePermissions = _.get(response, "data.permissions", []);
                    this.availablePermissions = JSON.parse(JSON.stringify(_availablePermissions));
                    this.availableJobTitles = _.get(response, "data.jobTitles", null);
                    this.findSMSPermissionId(this.availablePermissions);
                    this.isLoading = false;
                })
                .catch((error) => {
                    console.error(error);
                    this.isLoading = false;
                });
        },
        findSMSPermissionId(permissions) {
            for (const permission of permissions) {
                const isSMSPermission = permission.id && permission.id === 41;

                if (isSMSPermission) {
                    this.smsPermissionId = permission.id;
                    return;
                }

                if (permission.children && permission.children.length > 0) {
                    this.findSMSPermissionId(permission.children);
                }
            }
        },
        associateUser() {
            this.isAddingUser = true;

            let finalPermissions = [...this.permissions];

            if (this.smsPermissionId !== null) {
                const hasSMSPermission = finalPermissions.includes(this.smsPermissionId);

                if (this.form.onlineSMSEnabled && !hasSMSPermission) {
                    finalPermissions.push(this.smsPermissionId);
                } else if (!this.form.onlineSMSEnabled && hasSMSPermission) {
                    finalPermissions = finalPermissions.filter((id) => id !== this.smsPermissionId);
                }
            }

            this.form.permissions = finalPermissions;

            api.post(`/dealer/${this.dealerId}/users/${this.dealerUser.id}`, this.form)
                .then((response) => {
                    this.$emit("done", response.data);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isAddingUser = false;
                });
        },
        onCancel() {
            this.$emit("cancel");
        },
    },
};
</script>
